export { renderers } from '../../renderers.mjs';

const CLIENT_ID = "Ov23liYrLSc03pEujzBl";
const GET = async ({ url }) => {
  try {
    const redirectUrl = new URL("https://github.com/login/oauth/authorize");
    redirectUrl.searchParams.set("client_id", CLIENT_ID);
    redirectUrl.searchParams.set("redirect_uri", url.origin + "/api/callback");
    redirectUrl.searchParams.set("scope", "repo user");
    redirectUrl.searchParams.set(
      "state",
      Math.random().toString(36).substring(2, 15)
    );
    return Response.redirect(redirectUrl.href, 301);
  } catch (error) {
    console.error("Auth error:", error);
    return new Response(error instanceof Error ? error.message : "Authentication failed", {
      status: 500
    });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

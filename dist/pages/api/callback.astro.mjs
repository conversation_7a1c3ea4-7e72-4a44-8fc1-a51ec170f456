export { renderers } from '../../renderers.mjs';

const CLIENT_ID = "Ov23liYrLSc03pEujzBl";
const CLIENT_SECRET = "1c0da22a02f7c717581a628bf2a7a2513f703c56";
function renderBody(status, content) {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorizing...</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .error {
      color: #dc3545;
    }
    .success {
      color: #28a745;
    }
  </style>
</head>
<body>
  <div class="container">
    ${status === "success" ? `
      <div class="spinner"></div>
      <h2 class="success">Authentication Successful!</h2>
      <p>Completing authorization...</p>
    ` : `
      <h2 class="error">Authentication Failed</h2>
      <p>There was an error during authentication.</p>
    `}
  </div>

  <script>
    (function() {
      function receiveMessage(e) {
        console.log("receiveMessage %o", e);
        
        ${status === "success" ? `
          // Send the token back to the parent window (Decap CMS)
          e.source.postMessage(
            "authorization:github:success:" + JSON.stringify({
              token: "${content.token}",
              provider: "${content.provider}"
            }),
            e.origin
          );
        ` : `
          // Send error message to parent
          e.source.postMessage(
            "authorization:github:error:" + JSON.stringify({
              error: "${content.error || "Authentication failed"}"
            }),
            e.origin
          );
        `}
      }
      
      window.addEventListener("message", receiveMessage, false);
      
      // Also try to send the message immediately in case the parent is ready
      if (window.opener) {
        ${status === "success" ? `
          window.opener.postMessage(
            "authorization:github:success:" + JSON.stringify({
              token: "${content.token}",
              provider: "${content.provider}"
            }),
            window.location.origin
          );
        ` : `
          window.opener.postMessage(
            "authorization:github:error:" + JSON.stringify({
              error: "${content.error || "Authentication failed"}"
            }),
            window.location.origin
          );
        `}
      }
    })();
  </script>
</body>
</html>`;
  return html;
}
const GET = async ({ url }) => {
  try {
    if (!CLIENT_SECRET) ;
    const code = url.searchParams.get("code");
    if (!code) {
      throw new Error("No authorization code received");
    }
    const response = await fetch("https://github.com/login/oauth/access_token", {
      method: "POST",
      headers: {
        "content-type": "application/json",
        "user-agent": "astro-decap-cms-oauth",
        "accept": "application/json"
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code
      })
    });
    const result = await response.json();
    if (result.error) {
      const errorHtml = renderBody("error", { error: result.error_description || result.error });
      return new Response(errorHtml, {
        headers: {
          "content-type": "text/html;charset=UTF-8"
        },
        status: 401
      });
    }
    const token = result.access_token;
    const provider = "github";
    const successHtml = renderBody("success", { token, provider });
    return new Response(successHtml, {
      headers: {
        "content-type": "text/html;charset=UTF-8"
      },
      status: 200
    });
  } catch (error) {
    console.error("Callback error:", error);
    const errorHtml = renderBody("error", {
      error: error instanceof Error ? error.message : "Authentication failed"
    });
    return new Response(errorHtml, {
      headers: {
        "content-type": "text/html;charset=UTF-8"
      },
      status: 500
    });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

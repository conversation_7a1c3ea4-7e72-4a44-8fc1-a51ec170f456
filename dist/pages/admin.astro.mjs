import { c as createComponent, r as renderHead, a as renderScript, b as renderTemplate } from '../chunks/astro/server_67HHtGHz.mjs';
import 'kleur/colors';
import 'clsx';
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`<html> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Content Manager</title><meta name="robots" content="noindex">${renderHead()}</head> <body> <!-- Include the script that builds the page and powers Decap CMS --> ${renderScript($$result, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro?astro&type=script&index=0&lang.ts")} ${renderScript($$result, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro?astro&type=script&index=1&lang.ts")} </body> </html>`;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro";
const $$url = "/admin";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

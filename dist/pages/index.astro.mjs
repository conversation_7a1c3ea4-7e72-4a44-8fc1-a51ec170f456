import { c as createComponent, e as renderComponent, b as renderTemplate, m as maybeRenderHead } from '../chunks/astro/server_67HHtGHz.mjs';
import 'kleur/colors';
import { g as getCollection, $ as $$Layout } from '../chunks/_astro_content_1g8q-oV5.mjs';
import { $ as $$ProjectCard } from '../chunks/ProjectCard_Cn1c8y91.mjs';
import { $ as $$BlogCard } from '../chunks/BlogCard_CPB9tvvS.mjs';
/* empty css                                 */
export { renderers } from '../renderers.mjs';

const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const allProjects = await getCollection("projects");
  const featuredProjects = allProjects.filter((project) => project.data.featured).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime()).slice(0, 3);
  const allBlogPosts = await getCollection("blog");
  const recentPosts = allBlogPosts.filter((post) => !post.data.draft).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime()).slice(0, 3);
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Guntur - Software Developer", "description": "Portfolio website of Guntur - Software Developer specializing in web development and modern technologies.", "data-astro-cid-j7pv25f6": true }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<section class="hero" data-astro-cid-j7pv25f6> <div class="hero-content" data-astro-cid-j7pv25f6> <h1 class="hero-title" data-astro-cid-j7pv25f6>
Hi, I'm <span class="highlight" data-astro-cid-j7pv25f6>Guntur</span> </h1> <p class="hero-subtitle" data-astro-cid-j7pv25f6>
Software Developer passionate about creating innovative web solutions
</p> <p class="hero-description" data-astro-cid-j7pv25f6>
I specialize in modern web technologies and love building applications that make a difference.
        Welcome to my portfolio where you can explore my projects and read about my journey in tech.
</p> <div class="hero-actions" data-astro-cid-j7pv25f6> <a href="/projects" class="btn btn-primary" data-astro-cid-j7pv25f6>View Projects</a> <a href="/blog" class="btn btn-secondary" data-astro-cid-j7pv25f6>Read Blog</a> </div> </div> </section>  ${featuredProjects.length > 0 && renderTemplate`<section class="featured-projects" data-astro-cid-j7pv25f6> <h2 class="section-title" data-astro-cid-j7pv25f6>Featured Projects</h2> <div class="projects-grid" data-astro-cid-j7pv25f6> ${featuredProjects.map((project) => renderTemplate`${renderComponent($$result2, "ProjectCard", $$ProjectCard, { "title": project.data.title, "description": project.data.description, "image": project.data.image, "technologies": project.data.technologies, "liveUrl": project.data.liveUrl, "githubUrl": project.data.githubUrl, "slug": project.slug, "featured": project.data.featured, "data-astro-cid-j7pv25f6": true })}`)} </div> <div class="section-footer" data-astro-cid-j7pv25f6> <a href="/projects" class="view-all-link" data-astro-cid-j7pv25f6>View All Projects →</a> </div> </section>`} ${recentPosts.length > 0 && renderTemplate`<section class="recent-posts" data-astro-cid-j7pv25f6> <h2 class="section-title" data-astro-cid-j7pv25f6>Recent Blog Posts</h2> <div class="posts-grid" data-astro-cid-j7pv25f6> ${recentPosts.map((post) => renderTemplate`${renderComponent($$result2, "BlogCard", $$BlogCard, { "title": post.data.title, "description": post.data.description, "publishDate": post.data.publishDate, "image": post.data.image, "tags": post.data.tags, "slug": post.slug, "featured": post.data.featured, "data-astro-cid-j7pv25f6": true })}`)} </div> <div class="section-footer" data-astro-cid-j7pv25f6> <a href="/blog" class="view-all-link" data-astro-cid-j7pv25f6>View All Posts →</a> </div> </section>`}` })} `;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/index.astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

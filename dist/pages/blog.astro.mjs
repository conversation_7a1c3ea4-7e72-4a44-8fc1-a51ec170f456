import { c as createComponent, e as renderComponent, a as renderScript, b as renderTemplate, m as maybeRenderHead, f as addAttribute } from '../chunks/astro/server_67HHtGHz.mjs';
import 'kleur/colors';
import { g as getCollection, $ as $$Layout } from '../chunks/_astro_content_1g8q-oV5.mjs';
import { $ as $$BlogCard } from '../chunks/BlogCard_CPB9tvvS.mjs';
/* empty css                                */
export { renderers } from '../renderers.mjs';

const $$Blog = createComponent(async ($$result, $$props, $$slots) => {
  const allBlogPosts = await getCollection("blog");
  const posts = allBlogPosts.filter((post) => !post.data.draft).sort((a, b) => b.data.publishDate.getTime() - a.data.publishDate.getTime());
  const allTags = [...new Set(posts.flatMap((post) => post.data.tags))].sort();
  return renderTemplate`${renderComponent($$result, "Layout", $$Layout, { "title": "Blog - Guntur", "description": "Read my thoughts on web development, technology, and programming.", "data-astro-cid-ijnerlr2": true }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="blog-header" data-astro-cid-ijnerlr2> <h1 class="page-title" data-astro-cid-ijnerlr2>Blog</h1> <p class="page-description" data-astro-cid-ijnerlr2>
Thoughts, tutorials, and insights about web development, technology, and my journey as a software developer.
</p> </div> ${posts.length > 0 ? renderTemplate`<div class="blog-container" data-astro-cid-ijnerlr2> <!-- Filter Section --> ${allTags.length > 0 && renderTemplate`<div class="filters" data-astro-cid-ijnerlr2> <h3 data-astro-cid-ijnerlr2>Filter by Tag:</h3> <div class="filter-tags" data-astro-cid-ijnerlr2> <button class="filter-tag active" data-filter="all" data-astro-cid-ijnerlr2>All</button> ${allTags.map((tag) => renderTemplate`<button class="filter-tag"${addAttribute(tag.toLowerCase(), "data-filter")} data-astro-cid-ijnerlr2>${tag}</button>`)} </div> </div>`} <!-- Blog Posts Grid --> <div class="posts-grid" id="posts-grid" data-astro-cid-ijnerlr2> ${posts.map((post) => renderTemplate`<div class="post-item"${addAttribute(post.data.tags.map((t) => t.toLowerCase()).join(","), "data-tags")} data-astro-cid-ijnerlr2> ${renderComponent($$result2, "BlogCard", $$BlogCard, { "title": post.data.title, "description": post.data.description, "publishDate": post.data.publishDate, "image": post.data.image, "tags": post.data.tags, "slug": post.slug, "featured": post.data.featured, "data-astro-cid-ijnerlr2": true })} </div>`)} </div> </div>` : renderTemplate`<div class="empty-state" data-astro-cid-ijnerlr2> <h2 data-astro-cid-ijnerlr2>No Blog Posts Yet</h2> <p data-astro-cid-ijnerlr2>Blog posts will appear here once they're published through the CMS.</p> </div>`}` })}  ${renderScript($$result, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro?astro&type=script&index=0&lang.ts")}`;
}, "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro", void 0);

const $$file = "/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro";
const $$url = "/blog";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Blog,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

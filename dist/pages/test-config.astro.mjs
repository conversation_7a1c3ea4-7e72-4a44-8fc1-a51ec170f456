export { renderers } from '../renderers.mjs';

const GET = async ({ url }) => {
  const configResponse = await fetch(`${url.origin}/admin/config.yml`);
  const configContent = await configResponse.text();
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Config Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .config { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; padding: 10px; border-radius: 4px; margin: 10px 0; }
    pre { white-space: pre-wrap; }
  </style>
</head>
<body>
  <h1>Configuration Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}<br>
    <strong>Origin:</strong> ${url.origin}<br>
    <strong>Hostname:</strong> ${url.hostname}
  </div>
  
  <h2>Config Content (as Decap CMS sees it)</h2>
  <div class="config">
    <pre>${configContent}</pre>
  </div>
  
  <h2>Expected Auth URL</h2>
  <div class="info">
    Based on the config above, Decap CMS should try to open:<br>
    <strong>${url.origin}/auth</strong>
  </div>
  
  <h2>Test Auth URL</h2>
  <button onclick="testAuthUrl()">Test Auth URL</button>
  <div id="test-result"></div>
  
  <script>
    async function testAuthUrl() {
      const resultDiv = document.getElementById('test-result');
      resultDiv.innerHTML = 'Testing...';
      
      try {
        const response = await fetch('/auth');
        if (response.ok) {
          resultDiv.innerHTML = '<div style="color: green;">✓ Auth URL is accessible</div>';
        } else {
          resultDiv.innerHTML = '<div style="color: red;">✗ Auth URL returned status: ' + response.status + '</div>';
        }
      } catch (error) {
        resultDiv.innerHTML = '<div style="color: red;">✗ Auth URL error: ' + error.message + '</div>';
      }
    }
  <\/script>
</body>
</html>`;
  return new Response(html, {
    status: 200,
    headers: {
      "Content-Type": "text/html"
    }
  });
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

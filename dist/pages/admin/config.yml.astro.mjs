import fs from 'fs';
import path from 'path';
export { renderers } from '../../renderers.mjs';

const GET = async ({ url }) => {
  try {
    const configPath = path.join(process.cwd(), "public", "admin", "config.yml");
    let configContent = fs.readFileSync(configPath, "utf-8");
    if (url.hostname === "localhost" || url.hostname === "127.0.0.1") {
      configContent = configContent.replace(
        "site_domain: https://bagusfarisa.vercel.app",
        `site_domain: ${url.origin}`
      );
      configContent = configContent.replace(
        "base_url: https://bagusfarisa.vercel.app",
        `base_url: ${url.origin}`
      );
    }
    return new Response(configContent, {
      status: 200,
      headers: {
        "Content-Type": "text/yaml",
        "Cache-Control": "no-cache"
      }
    });
  } catch (error) {
    console.error("Error reading config file:", error);
    return new Response("Error loading config", { status: 500 });
  }
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

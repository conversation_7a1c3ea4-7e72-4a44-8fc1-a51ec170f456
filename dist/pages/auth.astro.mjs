export { renderers } from '../renderers.mjs';

const CLIENT_ID = "Ov23liYrLSc03pEujzBl";
const CLIENT_SECRET = "1c0da22a02f7c717581a628bf2a7a2513f703c56";
const GET = async ({ url }) => {
  const code = url.searchParams.get("code");
  url.searchParams.get("state");
  const provider = url.searchParams.get("provider");
  if (code) {
    return handleCallback(url, code);
  }
  return handleAuth(url, provider);
};
const handleAuth = async (url, provider) => {
  if (provider && provider !== "github") {
    return new Response("Invalid provider", { status: 400 });
  }
  const redirectUri = `${url.origin}/auth`;
  const scope = "repo,user";
  const randomState = Math.random().toString(36).substring(2, 15);
  const githubAuthUrl = new URL("https://github.com/login/oauth/authorize");
  githubAuthUrl.searchParams.set("client_id", CLIENT_ID);
  githubAuthUrl.searchParams.set("redirect_uri", redirectUri);
  githubAuthUrl.searchParams.set("scope", scope);
  githubAuthUrl.searchParams.set("state", randomState);
  return Response.redirect(githubAuthUrl.toString(), 301);
};
const handleCallback = async (url, code, state) => {
  try {
    if (!CLIENT_SECRET) ;
    const redirectUri = `${url.origin}/auth`;
    const tokenResponse = await fetch("https://github.com/login/oauth/access_token", {
      method: "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code,
        redirect_uri: redirectUri
      })
    });
    const tokenData = await tokenResponse.json();
    if (tokenData.error) {
      throw new Error(`GitHub OAuth error: ${tokenData.error_description || tokenData.error}`);
    }
    if (!tokenData.access_token) {
      throw new Error("No access token received from GitHub");
    }
    const successHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorizing Decap...</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .auth-container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <div class="spinner"></div>
    <h2>Authorizing Decap...</h2>
    <p>Please wait while we complete the authentication process.</p>
  </div>

  <script>
    (function() {
      const token = "${tokenData.access_token}";

      function receiveMessage(e) {
        console.log("receiveMessage %o", e);

        // Send the token back to the parent window (Decap CMS)
        e.source.postMessage(
          "authorization:github:success:" + JSON.stringify({
            token: token,
            provider: "github"
          }),
          e.origin
        );
      }

      window.addEventListener("message", receiveMessage, false);

      // Also try to send the message immediately in case the parent is ready
      if (window.opener) {
        window.opener.postMessage(
          "authorization:github:success:" + JSON.stringify({
            token: token,
            provider: "github"
          }),
          "${url.origin}"
        );
      }
    })();
  </script>
</body>
</html>`;
    return new Response(successHtml, {
      status: 200,
      headers: {
        "Content-Type": "text/html"
      }
    });
  } catch (error) {
    console.error("OAuth error:", error);
    const errorMessage = error instanceof Error ? error.message : "Authentication failed";
    const errorHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorization Error</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .error-container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .error-icon {
      color: #dc3545;
      font-size: 48px;
      margin-bottom: 1rem;
    }
    .retry-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon">✗</div>
    <h2>Authentication Failed</h2>
    <p>${errorMessage}</p>
    <button class="retry-button" onclick="window.location.reload()">Try Again</button>
  </div>

  <script>
    (function() {
      const errorData = {
        error: "${errorMessage}",
        provider: 'github'
      };

      function receiveMessage(e) {
        // Send error message to parent
        e.source.postMessage(
          'authorization:github:error:' + JSON.stringify(errorData),
          e.origin
        );
      }

      window.addEventListener("message", receiveMessage, false);

      // Send the error message immediately
      if (window.opener) {
        window.opener.postMessage(
          'authorization:github:error:' + JSON.stringify(errorData),
          "${url.origin}"
        );
      }
    })();
  </script>
</body>
</html>`;
    return new Response(errorHtml, {
      status: 400,
      headers: {
        "Content-Type": "text/html"
      }
    });
  }
};
const POST = async ({ request, redirect }) => {
  return redirect("/admin/");
};

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GET,
  POST
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };

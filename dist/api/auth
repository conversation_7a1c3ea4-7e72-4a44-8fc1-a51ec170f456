<!DOCTYPE html>
<html>
<head>
  <title>Redirecting to GitHub...</title>
  <meta name="robots" content="noindex">
  <meta http-equiv="refresh" content="0; url=https://github.com/login/oauth/authorize?client_id=Ov23liYrLSc03pEujzBl&redirect_uri=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fapi%2Fcallback&scope=repo+user&state=agt58jy4r0w">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 2s linear infinite;
      margin: 0 auto 1rem;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="spinner"></div>
    <h2>Redirecting to GitHub...</h2>
    <p>Please wait while we redirect you to GitHub for authentication.</p>
    <p><a href="https://github.com/login/oauth/authorize?client_id=Ov23liYrLSc03pEujzBl&redirect_uri=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fapi%2Fcallback&scope=repo+user&state=agt58jy4r0w">Click here if you are not redirected automatically</a></p>
  </div>

  <script>
    // Fallback JavaScript redirect
    setTimeout(function() {
      window.location.href = "https://github.com/login/oauth/authorize?client_id=Ov23liYrLSc03pEujzBl&redirect_uri=https%3A%2F%2Fbagusfarisa-astro.vercel.app%2Fapi%2Fcallback&scope=repo+user&state=agt58jy4r0w";
    }, 1000);
  </script>
</body>
</html>
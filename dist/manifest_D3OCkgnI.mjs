import '@astrojs/internal-helpers/path';
import 'kleur/colors';
import { N as NOOP_MIDDLEWARE_HEADER, k as decodeKey } from './chunks/astro/server_67HHtGHz.mjs';
import 'clsx';
import 'cookie';
import 'es-module-lexer';
import 'html-escaper';

const NOOP_MIDDLEWARE_FN = async (_ctx, next) => {
  const response = await next();
  response.headers.set(NOOP_MIDDLEWARE_HEADER, "true");
  return response;
};

const codeToStatusMap = {
  // Implemented from IANA HTTP Status Code Registry
  // https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  PAYMENT_REQUIRED: 402,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  NOT_ACCEPTABLE: 406,
  PROXY_AUTHENTICATION_REQUIRED: 407,
  REQUEST_TIMEOUT: 408,
  CONFLICT: 409,
  GONE: 410,
  LENGTH_REQUIRED: 411,
  PRECONDITION_FAILED: 412,
  CONTENT_TOO_LARGE: 413,
  URI_TOO_LONG: 414,
  UNSUPPORTED_MEDIA_TYPE: 415,
  RANGE_NOT_SATISFIABLE: 416,
  EXPECTATION_FAILED: 417,
  MISDIRECTED_REQUEST: 421,
  UNPROCESSABLE_CONTENT: 422,
  LOCKED: 423,
  FAILED_DEPENDENCY: 424,
  TOO_EARLY: 425,
  UPGRADE_REQUIRED: 426,
  PRECONDITION_REQUIRED: 428,
  TOO_MANY_REQUESTS: 429,
  REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
  UNAVAILABLE_FOR_LEGAL_REASONS: 451,
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
  HTTP_VERSION_NOT_SUPPORTED: 505,
  VARIANT_ALSO_NEGOTIATES: 506,
  INSUFFICIENT_STORAGE: 507,
  LOOP_DETECTED: 508,
  NETWORK_AUTHENTICATION_REQUIRED: 511
};
Object.entries(codeToStatusMap).reduce(
  // reverse the key-value pairs
  (acc, [key, value]) => ({ ...acc, [value]: key }),
  {}
);

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/","cacheDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/node_modules/.astro/","outDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/","srcDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/","publicDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/public/","buildClientDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/client/","buildServerDir":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/server/","adapterName":"","routes":[{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/admin/config.yml","links":[],"scripts":[],"styles":[],"routeData":{"route":"/admin/config.yml","isIndex":false,"type":"endpoint","pattern":"^\\/admin\\/config\\.yml\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}],[{"content":"config.yml","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/config.yml.ts","pathname":"/admin/config.yml","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/admin/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/admin","isIndex":true,"type":"page","pattern":"^\\/admin\\/?$","segments":[[{"content":"admin","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/admin/index.astro","pathname":"/admin","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/api/auth","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/auth","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/auth\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/auth.ts","pathname":"/api/auth","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/api/callback","links":[],"scripts":[],"styles":[],"routeData":{"route":"/api/callback","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/callback\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"callback","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/callback.ts","pathname":"/api/callback","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/auth","links":[],"scripts":[],"styles":[],"routeData":{"route":"/auth","isIndex":false,"type":"endpoint","pattern":"^\\/auth\\/?$","segments":[[{"content":"auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/auth.ts","pathname":"/auth","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/blog/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/blog","isIndex":false,"type":"page","pattern":"^\\/blog\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog.astro","pathname":"/blog","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/debug-auth","links":[],"scripts":[],"styles":[],"routeData":{"route":"/debug-auth","isIndex":false,"type":"endpoint","pattern":"^\\/debug-auth\\/?$","segments":[[{"content":"debug-auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/debug-auth.ts","pathname":"/debug-auth","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/projects/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/projects","isIndex":false,"type":"page","pattern":"^\\/projects\\/?$","segments":[[{"content":"projects","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/projects.astro","pathname":"/projects","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/test-auth","links":[],"scripts":[],"styles":[],"routeData":{"route":"/test-auth","isIndex":false,"type":"endpoint","pattern":"^\\/test-auth\\/?$","segments":[[{"content":"test-auth","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-auth.ts","pathname":"/test-auth","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/test-config","links":[],"scripts":[],"styles":[],"routeData":{"route":"/test-config","isIndex":false,"type":"endpoint","pattern":"^\\/test-config\\/?$","segments":[[{"content":"test-config","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/test-config.ts","pathname":"/test-config","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/index.html","links":[],"scripts":[],"styles":[],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":true,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"https://bagusfarisa-astro.vercel.app","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro",{"propagation":"none","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog/[slug].astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/index.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro",{"propagation":"in-tree","containsHead":true}],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects/[slug].astro",{"propagation":"in-tree","containsHead":true}],["\u0000astro:content",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/blog@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/blog/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/index@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/projects@_@astro",{"propagation":"in-tree","containsHead":false}],["\u0000@astro-page:src/pages/projects/[slug]@_@astro",{"propagation":"in-tree","containsHead":false}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000noop-middleware":"_noop-middleware.mjs","\u0000noop-actions":"_noop-actions.mjs","\u0000@astro-page:src/pages/admin/config.yml@_@ts":"pages/admin/config.yml.astro.mjs","\u0000@astro-page:src/pages/admin/index@_@astro":"pages/admin.astro.mjs","\u0000@astro-page:src/pages/api/auth@_@ts":"pages/api/auth.astro.mjs","\u0000@astro-page:src/pages/api/callback@_@ts":"pages/api/callback.astro.mjs","\u0000@astro-page:src/pages/auth@_@ts":"pages/auth.astro.mjs","\u0000@astro-page:src/pages/blog/[slug]@_@astro":"pages/blog/_slug_.astro.mjs","\u0000@astro-page:src/pages/blog@_@astro":"pages/blog.astro.mjs","\u0000@astro-page:src/pages/debug-auth@_@ts":"pages/debug-auth.astro.mjs","\u0000@astro-page:src/pages/projects/[slug]@_@astro":"pages/projects/_slug_.astro.mjs","\u0000@astro-page:src/pages/projects@_@astro":"pages/projects.astro.mjs","\u0000@astro-page:src/pages/test-auth@_@ts":"pages/test-auth.astro.mjs","\u0000@astro-page:src/pages/test-config@_@ts":"pages/test-config.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astrojs-manifest":"manifest_D3OCkgnI.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/.astro/content-assets.mjs":"chunks/content-assets_DleWbedO.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/.astro/content-modules.mjs":"chunks/content-modules_Dz-S_Wwv.mjs","\u0000astro:data-layer-content":"chunks/_astro_data-layer-content_BwfnlRGP.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/node_modules/astro/dist/assets/services/sharp.js":"chunks/sharp_DFUko9VL.mjs","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro?astro&type=script&index=1&lang.ts":"_astro/index.astro_astro_type_script_index_1_lang.ClYIQhV0.js","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro?astro&type=script&index=0&lang.ts":"_astro/index.astro_astro_type_script_index_0_lang.CrHYTF53.js","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro?astro&type=script&index=0&lang.ts":"_astro/blog.astro_astro_type_script_index_0_lang.a6uhtcI5.js","/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro?astro&type=script&index=0&lang.ts":"_astro/projects.astro_astro_type_script_index_0_lang.UQqBg1RE.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/admin/index.astro?astro&type=script&index=0&lang.ts","console.log(\"Decap CMS Admin page loading...\");(window.location.hash===\"\"||window.location.hash===\"#\")&&window.location.replace(\"/admin/#/\");fetch(\"/admin/config.yml\").then(o=>o.text()).then(o=>{console.log(\"Loaded config:\",o)}).catch(o=>{console.error(\"Failed to load config:\",o)});console.log(\"Initializing Decap CMS...\");CMS.init({config:{load_config_file:!0}});CMS.registerEventListener({name:\"login\",handler:({author:o})=>{console.log(\"CMS Login event:\",o)}});window.addEventListener(\"message\",function(o){if(console.log(\"Received message:\",o),o.origin!==window.location.origin){console.log(\"Message from different origin, ignoring:\",o.origin);return}const e=o.data;if(console.log(\"Processing message:\",e),typeof e==\"string\"&&e.startsWith(\"authorization:github:\")){if(console.log(\"Received OAuth message:\",e),e.includes(\":success:\")){const n=JSON.parse(e.split(\":success:\")[1]);console.log(\"OAuth success:\",n)}else if(e.includes(\":error:\")){const n=JSON.parse(e.split(\":error:\")[1]);console.error(\"OAuth error:\",n),alert(\"Authentication failed: \"+n.error)}}else e&&e.type&&e.type.includes(\"authorization\")&&console.log(\"Received authorization message:\",e)});const i=window.open;window.open=function(...o){return console.log(\"window.open called with:\",o),console.log(\"URL being opened:\",o[0]),console.log(\"Window name:\",o[1]),console.log(\"Window features:\",o[2]),o[0]&&(o[0].includes(\"/auth\")||o[0]===\"about:blank\")&&(console.log(\"🚨 AUTH POPUP DETECTED!\"),console.log(\"Full URL:\",o[0]),o[0]===\"about:blank\"&&(console.error(\"❌ PROBLEM: Decap CMS is trying to open about:blank instead of the auth URL\"),console.log(\"This suggests a configuration issue with the auth endpoint\"))),i.apply(this,o)};"],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/blog.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",function(){const e=document.querySelectorAll(\".filter-tag\"),c=document.querySelectorAll(\".post-item\");e.forEach(i=>{i.addEventListener(\"click\",function(){e.forEach(t=>t.classList.remove(\"active\")),this.classList.add(\"active\");const s=this.getAttribute(\"data-filter\");c.forEach(t=>{if(s===\"all\")t.classList.remove(\"hidden\");else{const a=t.getAttribute(\"data-tags\");a&&a.includes(s)?t.classList.remove(\"hidden\"):t.classList.add(\"hidden\")}})})})});"],["/Users/<USER>/Documents/GitHub/bagusfarisa-astro/src/pages/projects.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",function(){const t=document.querySelectorAll(\".filter-tag\"),i=document.querySelectorAll(\".project-item\");t.forEach(o=>{o.addEventListener(\"click\",function(){t.forEach(e=>e.classList.remove(\"active\")),this.classList.add(\"active\");const s=this.getAttribute(\"data-filter\");i.forEach(e=>{if(s===\"all\")e.classList.remove(\"hidden\");else{const c=e.getAttribute(\"data-technologies\");c&&c.includes(s)?e.classList.remove(\"hidden\"):e.classList.add(\"hidden\")}})})})});"]],"assets":["/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/admin/config.yml","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/admin/index.html","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/api/auth","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/api/callback","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/auth","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/blog/index.html","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/debug-auth","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/projects/index.html","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/test-auth","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/test-config","/file:///Users/<USER>/Documents/GitHub/bagusfarisa-astro/dist/index.html"],"buildFormat":"directory","checkOrigin":false,"serverIslandNameMap":[],"key":"caUP6OqkkW13Rakq+FoUNFVSv85VDBnvZE3srUMIJl4="});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = null;

export { manifest };

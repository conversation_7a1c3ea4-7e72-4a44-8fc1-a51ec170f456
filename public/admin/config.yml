backend:
  name: github
  repo: bagusfarisa/bagusfarisa-astro
  branch: main
  auth_type: external_oauth
  base_url: https://bagusfarisa.vercel.app
  auth_endpoint: auth

# Uncomment below to enable drafts
# publish_mode: editorial_workflow

media_folder: public/images/uploads # Media files will be stored in the repo under public/images/uploads
public_folder: /images/uploads # The src attribute for uploaded media will begin with /images/uploads

collections:
  - name: "projects" # Used in routes, e.g., /admin/collections/projects
    label: "Projects" # Used in the UI
    folder: "src/content/projects" # The path to the folder where the documents are stored
    create: true # Allow users to create new documents in this collection
    slug: "{{slug}}" # Filename template, e.g., YYYY-MM-DD-title.md
    fields: # The fields for each document, usually in front matter
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Featured Image", name: "image", widget: "image", required: false }
      - label: "Gallery"
        name: "gallery"
        widget: "list"
        required: false
        field: { label: "Image", name: "image", widget: "image" }
      - label: "Technologies"
        name: "technologies"
        widget: "list"
        default: []
        field: { label: "Technology", name: "tech", widget: "string" }
      - { label: "Live URL", name: "liveUrl", widget: "string", required: false }
      - { label: "GitHub URL", name: "githubUrl", widget: "string", required: false }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }
      - { label: "Publish Date", name: "publishDate", widget: "datetime" }
      - label: "Status"
        name: "status"
        widget: "select"
        options: ["completed", "in-progress", "planned"]
        default: "completed"
      - { label: "Body", name: "body", widget: "markdown" }

  - name: "blog" # Used in routes, e.g., /admin/collections/blog
    label: "Blog" # Used in the UI
    folder: "src/content/blog" # The path to the folder where the documents are stored
    create: true # Allow users to create new documents in this collection
    slug: "{{year}}-{{month}}-{{day}}-{{slug}}" # Filename template, e.g., YYYY-MM-DD-title.md
    fields: # The fields for each document, usually in front matter
      - { label: "Title", name: "title", widget: "string" }
      - { label: "Description", name: "description", widget: "text" }
      - { label: "Publish Date", name: "publishDate", widget: "datetime" }
      - { label: "Updated Date", name: "updatedDate", widget: "datetime", required: false }
      - { label: "Author", name: "author", widget: "string", default: "Guntur" }
      - { label: "Featured Image", name: "image", widget: "image", required: false }
      - label: "Tags"
        name: "tags"
        widget: "list"
        default: []
        field: { label: "Tag", name: "tag", widget: "string" }
      - { label: "Category", name: "category", widget: "string", required: false }
      - { label: "Featured", name: "featured", widget: "boolean", default: false }
      - { label: "Draft", name: "draft", widget: "boolean", default: false }
      - { label: "Body", name: "body", widget: "markdown" }

  - name: "pages"
    label: "Pages"
    files:
      - label: "Home Page"
        name: "home"
        file: "src/content/pages/home.md"
        fields:
          - { label: "Title", name: "title", widget: "string" }
          - { label: "Hero Title", name: "heroTitle", widget: "string" }
          - { label: "Hero Subtitle", name: "heroSubtitle", widget: "string" }
          - { label: "Hero Description", name: "heroDescription", widget: "text" }
          - { label: "Body", name: "body", widget: "markdown" }

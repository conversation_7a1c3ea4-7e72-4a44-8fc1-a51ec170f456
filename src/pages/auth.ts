import type { APIRoute } from 'astro';

const CLIENT_ID = 'Ov23liYrLSc03pEujzBl';
const CLIENT_SECRET = import.meta.env.GITHUB_CLIENT_SECRET;

export const GET: APIRoute = async ({ url, redirect }) => {
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');

  // If no code is provided, show the OAuth initiation page
  if (!code) {
    const redirectUri = `${url.origin}/auth`;
    const scope = 'repo,user';

    // Generate a random state for security
    const randomState = Math.random().toString(36).substring(2, 15);

    const githubAuthUrl = new URL('https://github.com/login/oauth/authorize');
    githubAuthUrl.searchParams.set('client_id', CLIENT_ID);
    githubAuthUrl.searchParams.set('redirect_uri', redirectUri);
    githubAuthUrl.searchParams.set('scope', scope);
    githubAuthUrl.searchParams.set('state', randomState);

    // Instead of redirecting immediately, show an HTML page that handles the OAuth flow
    const authInitHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>GitHub Authentication</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .auth-container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .github-logo {
      width: 48px;
      height: 48px;
      margin-bottom: 1rem;
    }
    .auth-button {
      background: #24292e;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      text-decoration: none;
      display: inline-block;
      margin-top: 1rem;
    }
    .auth-button:hover {
      background: #1b1f23;
    }
    .loading {
      display: none;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
  <div class="auth-container">
    <svg class="github-logo" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
    </svg>
    <h2>Authenticate with GitHub</h2>
    <p>Click the button below to sign in with your GitHub account.</p>
    <button class="auth-button" onclick="startAuth()">Sign in with GitHub</button>
    <div class="loading" id="loading">
      <p>Redirecting to GitHub...</p>
    </div>
  </div>

  <script>
    function startAuth() {
      document.getElementById('loading').style.display = 'block';
      document.querySelector('.auth-button').style.display = 'none';

      // Redirect to GitHub OAuth
      window.location.href = '${githubAuthUrl.toString()}';
    }

    // Auto-start auth if this is opened in a popup
    if (window.opener) {
      setTimeout(startAuth, 1000);
    }
  </script>
</body>
</html>`;

    return new Response(authInitHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }

  // Exchange code for access token
  try {
    if (!CLIENT_SECRET) {
      throw new Error('GitHub client secret not configured');
    }

    const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code: code,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (tokenData.error) {
      throw new Error(`GitHub OAuth error: ${tokenData.error_description || tokenData.error}`);
    }

    if (!tokenData.access_token) {
      throw new Error('No access token received from GitHub');
    }

    // Create the success response that Decap CMS expects
    const successHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorization Success</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .success-container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .success-icon {
      color: #28a745;
      font-size: 48px;
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <div class="success-container">
    <div class="success-icon">✓</div>
    <h2>Authentication Successful!</h2>
    <p>You can now close this window.</p>
  </div>

  <script>
    (function() {
      const token = "${tokenData.access_token}";

      function sendMessage() {
        if (window.opener) {
          // Send the message in the format Decap CMS expects
          window.opener.postMessage({
            type: 'authorization-github',
            token: token,
            provider: 'github'
          }, "${url.origin}");

          // Also try the alternative format
          window.opener.postMessage(
            'authorization:github:success:' + JSON.stringify({
              token: token,
              provider: 'github'
            }),
            "${url.origin}"
          );

          // Close the popup after a short delay
          setTimeout(() => {
            window.close();
          }, 1000);
        }
      }

      // Send the message immediately
      sendMessage();

      // Also listen for messages from the parent
      window.addEventListener("message", function(e) {
        if (e.origin !== "${url.origin}") {
          return;
        }

        if (e.data === 'authorizing:github') {
          sendMessage();
        }
      });
    })();
  </script>
</body>
</html>`;

    return new Response(successHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('OAuth error:', error);

    const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
    const errorHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorization Error</title>
  <meta name="robots" content="noindex">
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      background: #f5f5f5;
    }
    .error-container {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      text-align: center;
      max-width: 400px;
    }
    .error-icon {
      color: #dc3545;
      font-size: 48px;
      margin-bottom: 1rem;
    }
    .retry-button {
      background: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 1rem;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-icon">✗</div>
    <h2>Authentication Failed</h2>
    <p>${errorMessage}</p>
    <button class="retry-button" onclick="window.location.reload()">Try Again</button>
  </div>

  <script>
    (function() {
      const errorData = {
        error: "${errorMessage}",
        provider: 'github'
      };

      function sendErrorMessage() {
        if (window.opener) {
          // Send error message to parent
          window.opener.postMessage({
            type: 'authorization-github-error',
            error: errorData.error,
            provider: 'github'
          }, "${url.origin}");

          // Also try the alternative format
          window.opener.postMessage(
            'authorization:github:error:' + JSON.stringify(errorData),
            "${url.origin}"
          );
        }
      }

      // Send the error message immediately
      sendErrorMessage();

      // Listen for messages from parent
      window.addEventListener("message", function(e) {
        if (e.origin !== "${url.origin}") {
          return;
        }

        if (e.data === 'authorizing:github') {
          sendErrorMessage();
        }
      });
    })();
  </script>
</body>
</html>`;

    return new Response(errorHtml, {
      status: 400,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
};

export const POST: APIRoute = async ({ request, redirect }) => {
  // Handle POST requests (if needed for OAuth flow)
  return redirect('/admin/');
};

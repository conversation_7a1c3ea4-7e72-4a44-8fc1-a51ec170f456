import type { APIRoute } from 'astro';

const CLIENT_ID = 'Ov23liYrLSc03pEujzBl';
const CLIENT_SECRET = import.meta.env.GITHUB_CLIENT_SECRET;

export const GET: APIRoute = async ({ url, redirect }) => {
  const code = url.searchParams.get('code');
  const state = url.searchParams.get('state');

  // If no code is provided, redirect to GitHub OAuth
  if (!code) {
    const redirectUri = `${url.origin}/auth`;
    const scope = 'repo,user';

    // Generate a random state for security
    const randomState = Math.random().toString(36).substring(2, 15);

    const githubAuthUrl = new URL('https://github.com/login/oauth/authorize');
    githubAuthUrl.searchParams.set('client_id', CLIENT_ID);
    githubAuthUrl.searchParams.set('redirect_uri', redirectUri);
    githubAuthUrl.searchParams.set('scope', scope);
    githubAuthUrl.searchParams.set('state', randomState);

    return redirect(githubAuthUrl.toString());
  }

  // Exchange code for access token
  try {
    if (!CLIENT_SECRET) {
      throw new Error('GitHub client secret not configured');
    }

    const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: CLIENT_ID,
        client_secret: CLIENT_SECRET,
        code: code,
      }),
    });

    const tokenData = await tokenResponse.json();

    if (tokenData.error) {
      throw new Error(`GitHub OAuth error: ${tokenData.error_description || tokenData.error}`);
    }

    if (!tokenData.access_token) {
      throw new Error('No access token received from GitHub');
    }

    // Create the success response that Decap CMS expects
    const successHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorization Success</title>
  <meta name="robots" content="noindex">
</head>
<body>
  <script>
    (function() {
      function receiveMessage(e) {
        console.log("receiveMessage %o", e);
        if (e.origin !== "${url.origin}") {
          console.log("Invalid origin: %o", e.origin);
          return;
        }

        // Send the token back to the parent window (Decap CMS)
        e.source.postMessage(
          'authorization:github:success:${JSON.stringify({
            token: tokenData.access_token,
            provider: 'github'
          })}',
          e.origin
        );
      }

      window.addEventListener("message", receiveMessage, false);

      // Also try to send the message immediately in case the parent is ready
      if (window.opener) {
        window.opener.postMessage(
          'authorization:github:success:${JSON.stringify({
            token: tokenData.access_token,
            provider: 'github'
          })}',
          "${url.origin}"
        );
      }
    })();
  </script>
  <p>Authorization successful! This window should close automatically.</p>
</body>
</html>`;

    return new Response(successHtml, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
      },
    });

  } catch (error) {
    console.error('OAuth error:', error);

    const errorHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Authorization Error</title>
  <meta name="robots" content="noindex">
</head>
<body>
  <script>
    (function() {
      function receiveMessage(e) {
        if (e.origin !== "${url.origin}") {
          return;
        }

        e.source.postMessage(
          'authorization:github:error:${JSON.stringify({
            error: error instanceof Error ? error.message : 'Authentication failed'
          })}',
          e.origin
        );
      }

      window.addEventListener("message", receiveMessage, false);

      if (window.opener) {
        window.opener.postMessage(
          'authorization:github:error:${JSON.stringify({
            error: error instanceof Error ? error.message : 'Authentication failed'
          })}',
          "${url.origin}"
        );
      }
    })();
  </script>
  <p>Authorization failed: ${error instanceof Error ? error.message : 'Unknown error'}</p>
</body>
</html>`;

    return new Response(errorHtml, {
      status: 400,
      headers: {
        'Content-Type': 'text/html',
      },
    });
  }
};

export const POST: APIRoute = async ({ request, redirect }) => {
  // Handle POST requests (if needed for OAuth flow)
  return redirect('/admin/');
};

---
// This page serves the Decap CMS admin interface at /admin/
// It's the same as /admin but handles the trailing slash route
---

<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Content Manager</title>
  <meta name="robots" content="noindex">
</head>
<body>
  <!-- Include the script that builds the page and powers Decap CMS -->
  <script src="https://unpkg.com/decap-cms@^3.0.0/dist/decap-cms.js"></script>
  <script>
    // Check if we need to redirect to the proper hash URL for Decap CMS
    if (window.location.hash === '' || window.location.hash === '#') {
      // Redirect to the correct admin URL with proper hash routing
      window.location.replace('/admin/#/');
    }

    // Initialize Decap CMS
    CMS.init({
      config: {
        load_config_file: true
      }
    });

    // Handle external OAuth messages
    window.addEventListener('message', function(event) {
      if (event.origin !== window.location.origin) {
        return;
      }

      const message = event.data;
      if (typeof message === 'string' && message.startsWith('authorization:github:')) {
        console.log('Received OAuth message:', message);

        if (message.includes(':success:')) {
          const tokenData = JSON.parse(message.split(':success:')[1]);
          console.log('OAuth success:', tokenData);

          // The CMS should handle this automatically, but we can add custom logic here if needed
        } else if (message.includes(':error:')) {
          const errorData = JSON.parse(message.split(':error:')[1]);
          console.error('OAuth error:', errorData);

          // Show error message to user
          alert('Authentication failed: ' + errorData.error);
        }
      }
    });
  </script>
</body>
</html>

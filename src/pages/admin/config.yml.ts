import type { APIRoute } from 'astro';
import fs from 'fs';
import path from 'path';

export const GET: APIRoute = async () => {
  try {
    // Read the config file from the public directory
    const configPath = path.join(process.cwd(), 'public', 'admin', 'config.yml');
    const configContent = fs.readFileSync(configPath, 'utf-8');
    
    return new Response(configContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/yaml',
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    console.error('Error reading config file:', error);
    return new Response('Error loading config', { status: 500 });
  }
};

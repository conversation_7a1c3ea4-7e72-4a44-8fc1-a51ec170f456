import type { APIRoute } from 'astro';
import fs from 'fs';
import path from 'path';

export const GET: APIRoute = async ({ url }) => {
  try {
    // Read the config file from the public directory
    const configPath = path.join(process.cwd(), 'public', 'admin', 'config.yml');
    let configContent = fs.readFileSync(configPath, 'utf-8');

    // For local development, update the base_url to use localhost
    if (url.hostname === 'localhost' || url.hostname === '127.0.0.1') {
      configContent = configContent.replace(
        'base_url: https://bagusfarisa.vercel.app',
        `base_url: ${url.origin}`
      );
    }

    return new Response(configContent, {
      status: 200,
      headers: {
        'Content-Type': 'text/yaml',
        'Cache-Control': 'no-cache'
      }
    });
  } catch (error) {
    console.error('Error reading config file:', error);
    return new Response('Error loading config', { status: 500 });
  }
};

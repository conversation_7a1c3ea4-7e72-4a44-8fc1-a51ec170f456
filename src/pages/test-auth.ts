import type { APIRoute } from 'astro';

export const GET: APIRoute = async ({ url }) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Auth Test</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; }
    .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
    .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    button { padding: 10px 20px; margin: 10px 0; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #0056b3; }
  </style>
</head>
<body>
  <h1>GitHub OAuth Test</h1>
  
  <div class="info">
    <strong>Current URL:</strong> ${url.href}
  </div>
  
  <div class="info">
    <strong>Auth Endpoint:</strong> <a href="/auth" target="_blank">/auth</a>
  </div>
  
  <div class="info">
    <strong>Admin Interface:</strong> <a href="/admin/" target="_blank">/admin/</a>
  </div>
  
  <h2>Test OAuth Flow</h2>
  <button onclick="testAuth()">Test GitHub OAuth</button>
  
  <h2>Configuration Check</h2>
  <div id="config-status">Checking configuration...</div>
  
  <script>
    function testAuth() {
      window.open('/auth', 'oauth-test', 'width=600,height=600');
    }
    
    // Check if auth endpoint is accessible
    fetch('/auth')
      .then(response => {
        const statusDiv = document.getElementById('config-status');
        if (response.ok || response.status === 302) {
          statusDiv.innerHTML = '<div class="success">✓ Auth endpoint is accessible</div>';
        } else {
          statusDiv.innerHTML = '<div class="error">✗ Auth endpoint returned status: ' + response.status + '</div>';
        }
      })
      .catch(error => {
        document.getElementById('config-status').innerHTML = 
          '<div class="error">✗ Auth endpoint error: ' + error.message + '</div>';
      });
  </script>
</body>
</html>`;

  return new Response(html, {
    status: 200,
    headers: {
      'Content-Type': 'text/html',
    },
  });
};
